# Doxygen config for xxHash
DOXYFILE_ENCODING      = UTF-8

PROJECT_NAME           = "xxHash"
PROJECT_NUMBER         = "0.8.2"
PROJECT_BRIEF          = "Extremely fast non-cryptographic hash function"
OUTPUT_DIRECTORY       = doxygen
OUTPUT_LANGUAGE        = English

# We already separate the internal docs.
INTERNAL_DOCS          = YES
# Consistency
SORT_MEMBER_DOCS       = NO
BRIEF_MEMBER_DESC      = YES
REPEAT_BRIEF           = YES

# Warnings
QUIET                  = YES
# Until we document everything
WARN_IF_UNDOCUMENTED   = NO

# TODO: Add the other files. It is just xxhash.h for now.
FILE_PATTERNS          = xxhash.h xxh_x86dispatch.c
# Note: xxHash's source files are technically ASCII only.
INPUT_ENCODING         = UTF-8
TAB_SIZE               = 4
MARKDOWN_SUPPORT       = YES

# xxHash is a C library
OPTIMIZE_OUTPUT_FOR_C  = YES
# So we can document the internals
EXTRACT_STATIC         = YES
# We show private part in the internal document
EXTRACT_PRIVATE        = YES
# Document the macros
MACRO_EXPANSION        = YES
EXPAND_ONLY_PREDEF     = YES
# Predefine some macros to clean up the output.
PREDEFINED             = "XXH_DOXYGEN=" \
                         "XXH_PUBLIC_API=" \
                         "XXH_NOESCAPE=" \
                         "XXH_FORCE_INLINE=static inline" \
                         "XXH_NO_INLINE=static" \
                         "XXH_RESTRICT=restrict" \
                         "XSUM_API=" \
                         "XXH_STATIC_LINKING_ONLY" \
                         "XXH_IMPLEMENTATION" \
                         "XXH_PUREF=[[gnu::pure]]" \
                         "XXH_CONSTF=[[gnu::const]]" \
                         "XXH_MALLOCF=[[gnu::malloc]]" \
                         "XXH_ALIGN(N)=alignas(N)" \
                         "XXH_ALIGN_MEMBER(align,type)=alignas(align) type"

# We want HTML docs
GENERATE_HTML          = YES
HTML_OUTPUT            = html
HTML_FILE_EXTENSION    = .html
# Tweak the colors a bit
HTML_COLORSTYLE_HUE    = 220
HTML_COLORSTYLE_GAMMA  = 100
HTML_COLORSTYLE_SAT    = 100

# We don't want LaTeX.
GENERATE_LATEX         = NO
