<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: packing.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">packing.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00889.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00554_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaa916ca426b2bb0343ba17e3753e245c2"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#gaa916ca426b2bb0343ba17e3753e245c2">packDouble2x32</a> (uvec2 const &amp;v)</td></tr>
<tr class="memdesc:gaa916ca426b2bb0343ba17e3753e245c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a double-qualifier value obtained by packing the components of v into a 64-bit value.  <a href="a00983.html#gaa916ca426b2bb0343ba17e3753e245c2">More...</a><br /></td></tr>
<tr class="separator:gaa916ca426b2bb0343ba17e3753e245c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20f134b07db3a3d3a38efb2617388c92"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga20f134b07db3a3d3a38efb2617388c92">packHalf2x16</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga20f134b07db3a3d3a38efb2617388c92"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a two-component floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification, and then packing these two 16- bit integers into a 32-bit unsigned integer.  <a href="a00983.html#ga20f134b07db3a3d3a38efb2617388c92">More...</a><br /></td></tr>
<tr class="separator:ga20f134b07db3a3d3a38efb2617388c92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga977ab172da5494e5ac63e952afacfbe2"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga977ab172da5494e5ac63e952afacfbe2">packSnorm2x16</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga977ab172da5494e5ac63e952afacfbe2"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00983.html#ga977ab172da5494e5ac63e952afacfbe2">More...</a><br /></td></tr>
<tr class="separator:ga977ab172da5494e5ac63e952afacfbe2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85e8f17627516445026ab7a9c2e3531a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga85e8f17627516445026ab7a9c2e3531a">packSnorm4x8</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga85e8f17627516445026ab7a9c2e3531a"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00983.html#ga85e8f17627516445026ab7a9c2e3531a">More...</a><br /></td></tr>
<tr class="separator:ga85e8f17627516445026ab7a9c2e3531a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0e2d107039fe608a209497af867b85fb"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga0e2d107039fe608a209497af867b85fb">packUnorm2x16</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga0e2d107039fe608a209497af867b85fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00983.html#ga0e2d107039fe608a209497af867b85fb">More...</a><br /></td></tr>
<tr class="separator:ga0e2d107039fe608a209497af867b85fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf7d2f7341a9eeb4a436929d6f9ad08f2"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#gaf7d2f7341a9eeb4a436929d6f9ad08f2">packUnorm4x8</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaf7d2f7341a9eeb4a436929d6f9ad08f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00983.html#gaf7d2f7341a9eeb4a436929d6f9ad08f2">More...</a><br /></td></tr>
<tr class="separator:gaf7d2f7341a9eeb4a436929d6f9ad08f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f4296dc5f12f0aa67ac05b8bb322483"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga5f4296dc5f12f0aa67ac05b8bb322483">unpackDouble2x32</a> (double v)</td></tr>
<tr class="memdesc:ga5f4296dc5f12f0aa67ac05b8bb322483"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a two-component unsigned integer vector representation of v.  <a href="a00983.html#ga5f4296dc5f12f0aa67ac05b8bb322483">More...</a><br /></td></tr>
<tr class="separator:ga5f4296dc5f12f0aa67ac05b8bb322483"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf59b52e6b28da9335322c4ae19b5d745"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#gaf59b52e6b28da9335322c4ae19b5d745">unpackHalf2x16</a> (uint v)</td></tr>
<tr class="memdesc:gaf59b52e6b28da9335322c4ae19b5d745"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a two-component floating-point vector with components obtained by unpacking a 32-bit unsigned integer into a pair of 16-bit values, interpreting those values as 16-bit floating-point numbers according to the OpenGL Specification, and converting them to 32-bit floating-point values.  <a href="a00983.html#gaf59b52e6b28da9335322c4ae19b5d745">More...</a><br /></td></tr>
<tr class="separator:gaf59b52e6b28da9335322c4ae19b5d745"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacd8f8971a3fe28418be0d0fa1f786b38"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#gacd8f8971a3fe28418be0d0fa1f786b38">unpackSnorm2x16</a> (uint p)</td></tr>
<tr class="memdesc:gacd8f8971a3fe28418be0d0fa1f786b38"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00983.html#gacd8f8971a3fe28418be0d0fa1f786b38">More...</a><br /></td></tr>
<tr class="separator:gacd8f8971a3fe28418be0d0fa1f786b38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2db488646d48b7c43d3218954523fe82"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga2db488646d48b7c43d3218954523fe82">unpackSnorm4x8</a> (uint p)</td></tr>
<tr class="memdesc:ga2db488646d48b7c43d3218954523fe82"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00983.html#ga2db488646d48b7c43d3218954523fe82">More...</a><br /></td></tr>
<tr class="separator:ga2db488646d48b7c43d3218954523fe82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f66188e5d65afeb9ffba1ad971e4007"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga1f66188e5d65afeb9ffba1ad971e4007">unpackUnorm2x16</a> (uint p)</td></tr>
<tr class="memdesc:ga1f66188e5d65afeb9ffba1ad971e4007"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00983.html#ga1f66188e5d65afeb9ffba1ad971e4007">More...</a><br /></td></tr>
<tr class="separator:ga1f66188e5d65afeb9ffba1ad971e4007"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f903259150b67e9466f5f8edffcd197"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00983.html#ga7f903259150b67e9466f5f8edffcd197">unpackUnorm4x8</a> (uint p)</td></tr>
<tr class="memdesc:ga7f903259150b67e9466f5f8edffcd197"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00983.html#ga7f903259150b67e9466f5f8edffcd197">More...</a><br /></td></tr>
<tr class="separator:ga7f903259150b67e9466f5f8edffcd197"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00889.html">Core features</a> </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd>
<dd>
<a class="el" href="a00907.html">GLM_GTC_packing</a> </dd></dl>

<p class="definition">Definition in file <a class="el" href="a00554_source.html">packing.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
