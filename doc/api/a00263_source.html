<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_relational.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_relational.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00263.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160; </div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_relational extension included&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;{</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="a00827.html#ga27e90dcb7941c9b70e295dc3f6f6369f">   37</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#ga5305af376173f1902719fa309bbae671">equal</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="a00827.html#ga8504f18a7e2bf315393032c2137dad83">   47</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#gaa5517341754149ffba742d230afd1f32">notEqual</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="a00827.html#gaf5d687d70d11708b68c36c6db5777040">   57</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#ga5305af376173f1902719fa309bbae671">equal</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, T <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160; </div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="a00827.html#gafa6a053e81179fa4292b35651c83c3fb">   67</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#ga5305af376173f1902719fa309bbae671">equal</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;C, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="a00827.html#ga29071147d118569344d10944b7d5c378">   77</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#gaa5517341754149ffba742d230afd1f32">notEqual</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, T <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="a00827.html#gad7959e14fbc35b4ed2617daf4d67f6cd">   87</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#gaa5517341754149ffba742d230afd1f32">notEqual</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;C, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a>);</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="a00827.html#gab3a93f19e72e9141f50527c9de21d0c0">   97</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#ga5305af376173f1902719fa309bbae671">equal</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, <span class="keywordtype">int</span> ULPs);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="a00827.html#ga5305af376173f1902719fa309bbae671">  107</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#ga5305af376173f1902719fa309bbae671">equal</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;C, int, Q&gt; <span class="keyword">const</span>&amp; ULPs);</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="a00827.html#gaa1cd7fc228ef6e26c73583fd0d9c6552">  117</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#gaa5517341754149ffba742d230afd1f32">notEqual</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, <span class="keywordtype">int</span> ULPs);</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160; </div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="a00827.html#gaa5517341754149ffba742d230afd1f32">  127</a></span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;C, bool, Q&gt; <a class="code" href="a00827.html#gaa5517341754149ffba742d230afd1f32">notEqual</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;C, int, Q&gt; <span class="keyword">const</span>&amp; ULPs);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160; </div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160; </div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="preprocessor">#include &quot;matrix_relational.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00827_html_gaa5517341754149ffba742d230afd1f32"><div class="ttname"><a href="a00827.html#gaa5517341754149ffba742d230afd1f32">glm::notEqual</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt; notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, int, Q &gt; const &amp;ULPs)</div><div class="ttdoc">Returns the component-wise comparison between two vectors in term of ULPs.</div></div>
<div class="ttc" id="aa00858_html_ga2a1e57fc5592b69cfae84174cbfc9429"><div class="ttname"><a href="a00858.html#ga2a1e57fc5592b69cfae84174cbfc9429">glm::epsilon</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType epsilon()</div><div class="ttdoc">Return the epsilon constant for floating point types.</div></div>
<div class="ttc" id="aa00827_html_ga5305af376173f1902719fa309bbae671"><div class="ttname"><a href="a00827.html#ga5305af376173f1902719fa309bbae671">glm::equal</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt; equal(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, int, Q &gt; const &amp;ULPs)</div><div class="ttdoc">Returns the component-wise comparison between two vectors in term of ULPs.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
