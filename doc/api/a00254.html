<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_int4x4_sized.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">matrix_int4x4_sized.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00824.html">GLM_EXT_matrix_int4x4_sized</a>  
<a href="#details">More...</a></p>

<p><a href="a00254_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga6ed05bec8bbe6c06fa9c7dc981c892f4"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int16, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#ga6ed05bec8bbe6c06fa9c7dc981c892f4">i16mat4</a></td></tr>
<tr class="memdesc:ga6ed05bec8bbe6c06fa9c7dc981c892f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">16 bit signed integer 4x4 matrix.  <a href="a00824.html#ga6ed05bec8bbe6c06fa9c7dc981c892f4">More...</a><br /></td></tr>
<tr class="separator:ga6ed05bec8bbe6c06fa9c7dc981c892f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e9865fa750ba5a1f2a78a64d3128e96"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int16, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#ga3e9865fa750ba5a1f2a78a64d3128e96">i16mat4x4</a></td></tr>
<tr class="memdesc:ga3e9865fa750ba5a1f2a78a64d3128e96"><td class="mdescLeft">&#160;</td><td class="mdescRight">16 bit signed integer 4x4 matrix.  <a href="a00824.html#ga3e9865fa750ba5a1f2a78a64d3128e96">More...</a><br /></td></tr>
<tr class="separator:ga3e9865fa750ba5a1f2a78a64d3128e96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae251517f782cb11ff0d9b79fcd13540e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int32, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#gae251517f782cb11ff0d9b79fcd13540e">i32mat4</a></td></tr>
<tr class="memdesc:gae251517f782cb11ff0d9b79fcd13540e"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit signed integer 4x4 matrix.  <a href="a00824.html#gae251517f782cb11ff0d9b79fcd13540e">More...</a><br /></td></tr>
<tr class="separator:gae251517f782cb11ff0d9b79fcd13540e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70bc41754e5d5a63ea90b04fd29a7e38"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int32, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#ga70bc41754e5d5a63ea90b04fd29a7e38">i32mat4x4</a></td></tr>
<tr class="memdesc:ga70bc41754e5d5a63ea90b04fd29a7e38"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit signed integer 4x4 matrix.  <a href="a00824.html#ga70bc41754e5d5a63ea90b04fd29a7e38">More...</a><br /></td></tr>
<tr class="separator:ga70bc41754e5d5a63ea90b04fd29a7e38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga137dbc4ffc97899595676e737df3de71"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int64, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#ga137dbc4ffc97899595676e737df3de71">i64mat4</a></td></tr>
<tr class="memdesc:ga137dbc4ffc97899595676e737df3de71"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit signed integer 4x4 matrix.  <a href="a00824.html#ga137dbc4ffc97899595676e737df3de71">More...</a><br /></td></tr>
<tr class="separator:ga137dbc4ffc97899595676e737df3de71"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa56fe396b8efe61daadfd55782e1df93"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int64, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#gaa56fe396b8efe61daadfd55782e1df93">i64mat4x4</a></td></tr>
<tr class="memdesc:gaa56fe396b8efe61daadfd55782e1df93"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit signed integer 4x4 matrix.  <a href="a00824.html#gaa56fe396b8efe61daadfd55782e1df93">More...</a><br /></td></tr>
<tr class="separator:gaa56fe396b8efe61daadfd55782e1df93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a13150a001c529121c442acf997c1da"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int8, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#ga5a13150a001c529121c442acf997c1da">i8mat4</a></td></tr>
<tr class="memdesc:ga5a13150a001c529121c442acf997c1da"><td class="mdescLeft">&#160;</td><td class="mdescRight">8 bit signed integer 4x4 matrix.  <a href="a00824.html#ga5a13150a001c529121c442acf997c1da">More...</a><br /></td></tr>
<tr class="separator:ga5a13150a001c529121c442acf997c1da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace28977d2e296a7d23fa466f55b114f7"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int8, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00824.html#gace28977d2e296a7d23fa466f55b114f7">i8mat4x4</a></td></tr>
<tr class="memdesc:gace28977d2e296a7d23fa466f55b114f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">8 bit signed integer 4x4 matrix.  <a href="a00824.html#gace28977d2e296a7d23fa466f55b114f7">More...</a><br /></td></tr>
<tr class="separator:gace28977d2e296a7d23fa466f55b114f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00824.html">GLM_EXT_matrix_int4x4_sized</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00889.html" title="Features that implement in C++ the GLSL specification as closely as possible.">Core features</a> (dependence) </dd></dl>

<p class="definition">Definition in file <a class="el" href="a00254_source.html">matrix_int4x4_sized.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
