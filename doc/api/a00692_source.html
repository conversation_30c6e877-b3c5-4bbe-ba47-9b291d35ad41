<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: optimum_pow.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">optimum_pow.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00692.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       error &quot;GLM: GLM_GTX_optimum_pow is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#elif GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTX_optimum_pow extension included&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;{</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00033"></a><span class="lineno"><a class="line" href="a00956.html#ga7288d7bb23f192bd64a60ba2a61a1c9f">   33</a></span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00956.html#ga7288d7bb23f192bd64a60ba2a61a1c9f">pow2</a>(genType <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="a00956.html#ga156e452c2d630001bb07f4a6f1060a10">   39</a></span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00956.html#ga156e452c2d630001bb07f4a6f1060a10">pow3</a>(genType <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="a00956.html#ga830b64f3b47818374e9d03cc20fadbf3">   45</a></span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00956.html#ga830b64f3b47818374e9d03cc20fadbf3">pow4</a>(genType <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">#include &quot;optimum_pow.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00956_html_ga7288d7bb23f192bd64a60ba2a61a1c9f"><div class="ttname"><a href="a00956.html#ga7288d7bb23f192bd64a60ba2a61a1c9f">glm::pow2</a></div><div class="ttdeci">GLM_FUNC_DECL genType pow2(genType const &amp;x)</div><div class="ttdoc">Returns x raised to the power of 2.</div></div>
<div class="ttc" id="aa00956_html_ga156e452c2d630001bb07f4a6f1060a10"><div class="ttname"><a href="a00956.html#ga156e452c2d630001bb07f4a6f1060a10">glm::pow3</a></div><div class="ttdeci">GLM_FUNC_DECL genType pow3(genType const &amp;x)</div><div class="ttdoc">Returns x raised to the power of 3.</div></div>
<div class="ttc" id="aa00956_html_ga830b64f3b47818374e9d03cc20fadbf3"><div class="ttname"><a href="a00956.html#ga830b64f3b47818374e9d03cc20fadbf3">glm::pow4</a></div><div class="ttdeci">GLM_FUNC_DECL genType pow4(genType const &amp;x)</div><div class="ttdoc">Returns x raised to the power of 4.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
