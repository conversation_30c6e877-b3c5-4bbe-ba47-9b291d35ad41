<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_matrix_uint4x2</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_matrix_uint4x2<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga03f2451aa64f11c36398dbc4db4b4ca5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, uint, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00841.html#ga03f2451aa64f11c36398dbc4db4b4ca5">umat4x2</a></td></tr>
<tr class="memdesc:ga03f2451aa64f11c36398dbc4db4b4ca5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 4x2 matrix.  <a href="a00841.html#ga03f2451aa64f11c36398dbc4db4b4ca5">More...</a><br /></td></tr>
<tr class="separator:ga03f2451aa64f11c36398dbc4db4b4ca5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00305.html" title="GLM_EXT_matrix_uint4x2">glm/ext/matrix_uint4x2.hpp</a>&gt; to use the features of this extension.</p>
<p>Defines a number of matrices with integer types. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga03f2451aa64f11c36398dbc4db4b4ca5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga03f2451aa64f11c36398dbc4db4b4ca5">&#9670;&nbsp;</a></span>umat4x2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, uint, defaultp &gt; <a class="el" href="a00841.html#ga03f2451aa64f11c36398dbc4db4b4ca5">umat4x2</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00841.html">GLM_EXT_matrix_uint4x2</a></dd>
<dd>
<a class="el" href="a00903.html">GLM_GTC_matrix_integer</a> </dd></dl>

<p class="definition">Definition at line <a class="el" href="a00305_source.html#l00030">30</a> of file <a class="el" href="a00305_source.html">matrix_uint4x2.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
