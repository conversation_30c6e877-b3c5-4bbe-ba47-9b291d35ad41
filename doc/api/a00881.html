<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_vector_relational</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_vector_relational<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2b46cb50911e97b32f4cd743c2c69771"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2b46cb50911e97b32f4cd743c2c69771"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga2b46cb50911e97b32f4cd743c2c69771">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, int ULPs)</td></tr>
<tr class="memdesc:ga2b46cb50911e97b32f4cd743c2c69771"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00881.html#ga2b46cb50911e97b32f4cd743c2c69771">More...</a><br /></td></tr>
<tr class="separator:ga2b46cb50911e97b32f4cd743c2c69771"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga2ac7651a2fa7354f2da610dbd50d28e2">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00881.html#ga2ac7651a2fa7354f2da610dbd50d28e2">More...</a><br /></td></tr>
<tr class="separator:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga7da2b8605be7f245b39cb6fbf6d9d581">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00881.html#ga7da2b8605be7f245b39cb6fbf6d9d581">More...</a><br /></td></tr>
<tr class="separator:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37d261a65f69babc82cec2ae1af7145f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga37d261a65f69babc82cec2ae1af7145f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga37d261a65f69babc82cec2ae1af7145f">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;epsilon)</td></tr>
<tr class="memdesc:ga37d261a65f69babc82cec2ae1af7145f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00881.html#ga37d261a65f69babc82cec2ae1af7145f">More...</a><br /></td></tr>
<tr class="separator:ga37d261a65f69babc82cec2ae1af7145f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga8b5c2c3f83422ae5b71fa960d03b0339">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, int ULPs)</td></tr>
<tr class="memdesc:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00881.html#ga8b5c2c3f83422ae5b71fa960d03b0339">More...</a><br /></td></tr>
<tr class="separator:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a99cc41341567567a608719449c1fac"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4a99cc41341567567a608719449c1fac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga4a99cc41341567567a608719449c1fac">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:ga4a99cc41341567567a608719449c1fac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00881.html#ga4a99cc41341567567a608719449c1fac">More...</a><br /></td></tr>
<tr class="separator:ga4a99cc41341567567a608719449c1fac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b15ffe32987a6029b14398eb0def01a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0b15ffe32987a6029b14398eb0def01a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga0b15ffe32987a6029b14398eb0def01a">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:ga0b15ffe32987a6029b14398eb0def01a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00881.html#ga0b15ffe32987a6029b14398eb0def01a">More...</a><br /></td></tr>
<tr class="separator:ga0b15ffe32987a6029b14398eb0def01a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga417cf51304359db18e819dda9bce5767"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga417cf51304359db18e819dda9bce5767"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00881.html#ga417cf51304359db18e819dda9bce5767">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;epsilon)</td></tr>
<tr class="memdesc:ga417cf51304359db18e819dda9bce5767"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00881.html#ga417cf51304359db18e819dda9bce5767">More...</a><br /></td></tr>
<tr class="separator:ga417cf51304359db18e819dda9bce5767"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes comparison functions for vector types that take a user defined epsilon values.</p>
<p>Include &lt;<a class="el" href="a01606.html" title="GLM_EXT_vector_relational">glm/ext/vector_relational.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd>core_vector_relational </dd>
<dd>
<a class="el" href="a00863.html">GLM_EXT_scalar_relational</a> </dd>
<dd>
<a class="el" href="a00827.html">GLM_EXT_matrix_relational</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga2b46cb50911e97b32f4cd743c2c69771"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2b46cb50911e97b32f4cd743c2c69771">&#9670;&nbsp;</a></span>equal() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::equal </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ULPs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison between two vectors in term of ULPs. </p>
<p>True if this expression is satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga2ac7651a2fa7354f2da610dbd50d28e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2ac7651a2fa7354f2da610dbd50d28e2">&#9670;&nbsp;</a></span>equal() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::equal </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &lt; epsilon. </p>
<p>True if this expression is satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga7da2b8605be7f245b39cb6fbf6d9d581"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7da2b8605be7f245b39cb6fbf6d9d581">&#9670;&nbsp;</a></span>equal() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::equal </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, int, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ULPs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison between two vectors in term of ULPs. </p>
<p>True if this expression is satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga37d261a65f69babc82cec2ae1af7145f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga37d261a65f69babc82cec2ae1af7145f">&#9670;&nbsp;</a></span>equal() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::equal </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &lt; epsilon. </p>
<p>True if this expression is satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga8b5c2c3f83422ae5b71fa960d03b0339"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8b5c2c3f83422ae5b71fa960d03b0339">&#9670;&nbsp;</a></span>notEqual() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::notEqual </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ULPs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison between two vectors in term of ULPs. </p>
<p>True if this expression is not satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga4a99cc41341567567a608719449c1fac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4a99cc41341567567a608719449c1fac">&#9670;&nbsp;</a></span>notEqual() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::notEqual </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &gt;= epsilon. </p>
<p>True if this expression is not satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga0b15ffe32987a6029b14398eb0def01a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0b15ffe32987a6029b14398eb0def01a">&#9670;&nbsp;</a></span>notEqual() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::notEqual </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, int, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ULPs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison between two vectors in term of ULPs. </p>
<p>True if this expression is not satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga417cf51304359db18e819dda9bce5767"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga417cf51304359db18e819dda9bce5767">&#9670;&nbsp;</a></span>notEqual() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; glm::notEqual </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &gt;= epsilon. </p>
<p>True if this expression is not satisfied.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
