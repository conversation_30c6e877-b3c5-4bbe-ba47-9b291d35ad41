<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: dual_quaternion.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_628fd60eb37daf5aa9a81e3983c640b7.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">dual_quaternion.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00926.html">GLM_GTX_dual_quaternion</a>  
<a href="#details">More...</a></p>

<p><a href="a00605_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga3d71f98d84ba59dfe4e369fde4714cd6"><td class="memItemLeft" align="right" valign="top">typedef highp_ddualquat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga3d71f98d84ba59dfe4e369fde4714cd6">ddualquat</a></td></tr>
<tr class="memdesc:ga3d71f98d84ba59dfe4e369fde4714cd6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of default double-qualifier floating-point numbers.  <a href="a00926.html#ga3d71f98d84ba59dfe4e369fde4714cd6">More...</a><br /></td></tr>
<tr class="separator:ga3d71f98d84ba59dfe4e369fde4714cd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae93abee0c979902fbec6a7bee0f6fae1"><td class="memItemLeft" align="right" valign="top">typedef highp_fdualquat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#gae93abee0c979902fbec6a7bee0f6fae1">dualquat</a></td></tr>
<tr class="memdesc:gae93abee0c979902fbec6a7bee0f6fae1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of floating-point numbers.  <a href="a00926.html#gae93abee0c979902fbec6a7bee0f6fae1">More...</a><br /></td></tr>
<tr class="separator:gae93abee0c979902fbec6a7bee0f6fae1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga237c2b9b42c9a930e49de5840ae0f930"><td class="memItemLeft" align="right" valign="top">typedef highp_fdualquat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga237c2b9b42c9a930e49de5840ae0f930">fdualquat</a></td></tr>
<tr class="memdesc:ga237c2b9b42c9a930e49de5840ae0f930"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of single-qualifier floating-point numbers.  <a href="a00926.html#ga237c2b9b42c9a930e49de5840ae0f930">More...</a><br /></td></tr>
<tr class="separator:ga237c2b9b42c9a930e49de5840ae0f930"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f67eafa7197d7a668dad5105a463d2a"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga8f67eafa7197d7a668dad5105a463d2a">highp_ddualquat</a></td></tr>
<tr class="memdesc:ga8f67eafa7197d7a668dad5105a463d2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of high double-qualifier floating-point numbers.  <a href="a00926.html#ga8f67eafa7197d7a668dad5105a463d2a">More...</a><br /></td></tr>
<tr class="separator:ga8f67eafa7197d7a668dad5105a463d2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ef5bf1da52a9d4932335a517086ceaf"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga9ef5bf1da52a9d4932335a517086ceaf">highp_dualquat</a></td></tr>
<tr class="memdesc:ga9ef5bf1da52a9d4932335a517086ceaf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of high single-qualifier floating-point numbers.  <a href="a00926.html#ga9ef5bf1da52a9d4932335a517086ceaf">More...</a><br /></td></tr>
<tr class="separator:ga9ef5bf1da52a9d4932335a517086ceaf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4c4e55e9c99dc57b299ed590968da564"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga4c4e55e9c99dc57b299ed590968da564">highp_fdualquat</a></td></tr>
<tr class="memdesc:ga4c4e55e9c99dc57b299ed590968da564"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of high single-qualifier floating-point numbers.  <a href="a00926.html#ga4c4e55e9c99dc57b299ed590968da564">More...</a><br /></td></tr>
<tr class="separator:ga4c4e55e9c99dc57b299ed590968da564"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab4c5103338af3dac7e0fbc86895a3f1a"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#gab4c5103338af3dac7e0fbc86895a3f1a">lowp_ddualquat</a></td></tr>
<tr class="memdesc:gab4c5103338af3dac7e0fbc86895a3f1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of low double-qualifier floating-point numbers.  <a href="a00926.html#gab4c5103338af3dac7e0fbc86895a3f1a">More...</a><br /></td></tr>
<tr class="separator:gab4c5103338af3dac7e0fbc86895a3f1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade05d29ebd4deea0f883d0e1bb4169aa"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#gade05d29ebd4deea0f883d0e1bb4169aa">lowp_dualquat</a></td></tr>
<tr class="memdesc:gade05d29ebd4deea0f883d0e1bb4169aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of low single-qualifier floating-point numbers.  <a href="a00926.html#gade05d29ebd4deea0f883d0e1bb4169aa">More...</a><br /></td></tr>
<tr class="separator:gade05d29ebd4deea0f883d0e1bb4169aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa38f671be25a7f3b136a452a8bb42860"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#gaa38f671be25a7f3b136a452a8bb42860">lowp_fdualquat</a></td></tr>
<tr class="memdesc:gaa38f671be25a7f3b136a452a8bb42860"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of low single-qualifier floating-point numbers.  <a href="a00926.html#gaa38f671be25a7f3b136a452a8bb42860">More...</a><br /></td></tr>
<tr class="separator:gaa38f671be25a7f3b136a452a8bb42860"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0fb11e48e2d16348ccb06a25213641b4"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga0fb11e48e2d16348ccb06a25213641b4">mediump_ddualquat</a></td></tr>
<tr class="memdesc:ga0fb11e48e2d16348ccb06a25213641b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of medium double-qualifier floating-point numbers.  <a href="a00926.html#ga0fb11e48e2d16348ccb06a25213641b4">More...</a><br /></td></tr>
<tr class="separator:ga0fb11e48e2d16348ccb06a25213641b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7aeb54c167712b38f2178a1be2360ad"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#gaa7aeb54c167712b38f2178a1be2360ad">mediump_dualquat</a></td></tr>
<tr class="memdesc:gaa7aeb54c167712b38f2178a1be2360ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of medium single-qualifier floating-point numbers.  <a href="a00926.html#gaa7aeb54c167712b38f2178a1be2360ad">More...</a><br /></td></tr>
<tr class="separator:gaa7aeb54c167712b38f2178a1be2360ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a6b594ff7e81150d8143001367a9431"><td class="memItemLeft" align="right" valign="top">typedef tdualquat&lt; float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00926.html#ga4a6b594ff7e81150d8143001367a9431">mediump_fdualquat</a></td></tr>
<tr class="memdesc:ga4a6b594ff7e81150d8143001367a9431"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual-quaternion of medium single-qualifier floating-point numbers.  <a href="a00926.html#ga4a6b594ff7e81150d8143001367a9431">More...</a><br /></td></tr>
<tr class="separator:ga4a6b594ff7e81150d8143001367a9431"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga0b35c0e30df8a875dbaa751e0bd800e0"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0b35c0e30df8a875dbaa751e0bd800e0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL tdualquat&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#ga0b35c0e30df8a875dbaa751e0bd800e0">dual_quat_identity</a> ()</td></tr>
<tr class="memdesc:ga0b35c0e30df8a875dbaa751e0bd800e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates an identity dual quaternion.  <a href="a00926.html#ga0b35c0e30df8a875dbaa751e0bd800e0">More...</a><br /></td></tr>
<tr class="separator:ga0b35c0e30df8a875dbaa751e0bd800e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac4064ff813759740201765350eac4236"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac4064ff813759740201765350eac4236"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL tdualquat&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#gac4064ff813759740201765350eac4236">dualquat_cast</a> (mat&lt; 2, 4, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gac4064ff813759740201765350eac4236"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a 2 * 4 matrix (matrix which holds real and dual parts) to a quaternion.  <a href="a00926.html#gac4064ff813759740201765350eac4236">More...</a><br /></td></tr>
<tr class="separator:gac4064ff813759740201765350eac4236"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga91025ebdca0f4ea54da08497b00e8c84"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga91025ebdca0f4ea54da08497b00e8c84"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL tdualquat&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#ga91025ebdca0f4ea54da08497b00e8c84">dualquat_cast</a> (mat&lt; 3, 4, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga91025ebdca0f4ea54da08497b00e8c84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a 3 * 4 matrix (augmented matrix rotation + translation) to a quaternion.  <a href="a00926.html#ga91025ebdca0f4ea54da08497b00e8c84">More...</a><br /></td></tr>
<tr class="separator:ga91025ebdca0f4ea54da08497b00e8c84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga070f521a953f6461af4ab4cf8ccbf27e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga070f521a953f6461af4ab4cf8ccbf27e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL tdualquat&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#ga070f521a953f6461af4ab4cf8ccbf27e">inverse</a> (tdualquat&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga070f521a953f6461af4ab4cf8ccbf27e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the q inverse.  <a href="a00926.html#ga070f521a953f6461af4ab4cf8ccbf27e">More...</a><br /></td></tr>
<tr class="separator:ga070f521a953f6461af4ab4cf8ccbf27e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace8380112d16d33f520839cb35a4d173"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gace8380112d16d33f520839cb35a4d173"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL tdualquat&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#gace8380112d16d33f520839cb35a4d173">lerp</a> (tdualquat&lt; T, Q &gt; const &amp;x, tdualquat&lt; T, Q &gt; const &amp;y, T const &amp;a)</td></tr>
<tr class="memdesc:gace8380112d16d33f520839cb35a4d173"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the linear interpolation of two dual quaternion.  <a href="a00926.html#gace8380112d16d33f520839cb35a4d173">More...</a><br /></td></tr>
<tr class="separator:gace8380112d16d33f520839cb35a4d173"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae99d143b37f9cad4cd9285571aab685a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae99d143b37f9cad4cd9285571aab685a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#gae99d143b37f9cad4cd9285571aab685a">mat2x4_cast</a> (tdualquat&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gae99d143b37f9cad4cd9285571aab685a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 2 * 4 matrix.  <a href="a00926.html#gae99d143b37f9cad4cd9285571aab685a">More...</a><br /></td></tr>
<tr class="separator:gae99d143b37f9cad4cd9285571aab685a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf59f5bb69620d2891c3795c6f2639179"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf59f5bb69620d2891c3795c6f2639179"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#gaf59f5bb69620d2891c3795c6f2639179">mat3x4_cast</a> (tdualquat&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gaf59f5bb69620d2891c3795c6f2639179"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 3 * 4 matrix.  <a href="a00926.html#gaf59f5bb69620d2891c3795c6f2639179">More...</a><br /></td></tr>
<tr class="separator:gaf59f5bb69620d2891c3795c6f2639179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga299b8641509606b1958ffa104a162cfe"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga299b8641509606b1958ffa104a162cfe"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL tdualquat&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00926.html#ga299b8641509606b1958ffa104a162cfe">normalize</a> (tdualquat&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga299b8641509606b1958ffa104a162cfe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the normalized quaternion.  <a href="a00926.html#ga299b8641509606b1958ffa104a162cfe">More...</a><br /></td></tr>
<tr class="separator:ga299b8641509606b1958ffa104a162cfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00926.html">GLM_GTX_dual_quaternion</a> </p>
<dl class="section author"><dt>Author</dt><dd>Maksim Vorobiev (<a href="#" onclick="location.href='mai'+'lto:'+'mso'+'me'+'one'+'@g'+'mai'+'l.'+'com'; return false;">msome<span style="display: none;">.nosp@m.</span>one@<span style="display: none;">.nosp@m.</span>gmail<span style="display: none;">.nosp@m.</span>.com</a>)</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00889.html" title="Features that implement in C++ the GLSL specification as closely as possible.">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00899.html">GLM_GTC_constants</a> (dependence) </dd>
<dd>
<a class="el" href="a00908.html">GLM_GTC_quaternion</a> (dependence) </dd></dl>

<p class="definition">Definition in file <a class="el" href="a00605_source.html">dual_quaternion.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
