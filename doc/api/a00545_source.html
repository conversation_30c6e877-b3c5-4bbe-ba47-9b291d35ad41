<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_access.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_d9678a6a9012da969e0b059b39347945.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_access.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00545.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_matrix_access extension included&quot;)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;{</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="a00902.html#ga259e5ebd0f31ec3f83440f8cae7f5dba">   30</a></span>&#160;        GLM_FUNC_DECL <span class="keyword">typename</span> genType::row_type <a class="code" href="a00902.html#gaadcc64829aadf4103477679e48c7594f">row</a>(</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;                genType <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;                length_t index);</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="a00902.html#gaadcc64829aadf4103477679e48c7594f">   37</a></span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00902.html#gaadcc64829aadf4103477679e48c7594f">row</a>(</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                genType <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                length_t index,</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                <span class="keyword">typename</span> genType::row_type <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="a00902.html#ga96022eb0d3fae39d89fc7a954e59b374">   45</a></span>&#160;        GLM_FUNC_DECL <span class="keyword">typename</span> genType::col_type <a class="code" href="a00902.html#ga9e757377523890e8b80c5843dbe4dd15">column</a>(</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                genType <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                length_t index);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="a00902.html#ga9e757377523890e8b80c5843dbe4dd15">   52</a></span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00902.html#ga9e757377523890e8b80c5843dbe4dd15">column</a>(</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                genType <span class="keyword">const</span>&amp; m,</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                length_t index,</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                <span class="keyword">typename</span> genType::col_type <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#include &quot;matrix_access.inl&quot;</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00902_html_gaadcc64829aadf4103477679e48c7594f"><div class="ttname"><a href="a00902.html#gaadcc64829aadf4103477679e48c7594f">glm::row</a></div><div class="ttdeci">GLM_FUNC_DECL genType row(genType const &amp;m, length_t index, typename genType::row_type const &amp;x)</div><div class="ttdoc">Set a specific row to a matrix.</div></div>
<div class="ttc" id="aa00902_html_ga9e757377523890e8b80c5843dbe4dd15"><div class="ttname"><a href="a00902.html#ga9e757377523890e8b80c5843dbe4dd15">glm::column</a></div><div class="ttdeci">GLM_FUNC_DECL genType column(genType const &amp;m, length_t index, typename genType::col_type const &amp;x)</div><div class="ttdoc">Set a specific column to a matrix.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
