<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: quaternion_exponential.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">quaternion_exponential.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00850.html">GLM_EXT_quaternion_exponential</a>  
<a href="#details">More...</a></p>

<p><a href="a00332_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00855.html#gaab2d37ef7265819f1d2939b9dc2c52ac">exp</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a exponential of a quaternion.  <a href="a00855.html#gaab2d37ef7265819f1d2939b9dc2c52ac">More...</a><br /></td></tr>
<tr class="separator:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00855.html#gaa5f7b20e296671b16ce25a2ab7ad5473">log</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a logarithm of a quaternion.  <a href="a00855.html#gaa5f7b20e296671b16ce25a2ab7ad5473">More...</a><br /></td></tr>
<tr class="separator:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00855.html#ga4975ffcacd312a8c0bbd046a76c5607e">pow</a> (qua&lt; T, Q &gt; const &amp;q, T y)</td></tr>
<tr class="memdesc:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a quaternion raised to a power.  <a href="a00855.html#ga4975ffcacd312a8c0bbd046a76c5607e">More...</a><br /></td></tr>
<tr class="separator:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64b7b255ed7bcba616fe6b44470b022e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga64b7b255ed7bcba616fe6b44470b022e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00855.html#ga64b7b255ed7bcba616fe6b44470b022e">sqrt</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga64b7b255ed7bcba616fe6b44470b022e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the square root of a quaternion.  <a href="a00855.html#ga64b7b255ed7bcba616fe6b44470b022e">More...</a><br /></td></tr>
<tr class="separator:ga64b7b255ed7bcba616fe6b44470b022e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00850.html">GLM_EXT_quaternion_exponential</a> </p>

<p class="definition">Definition in file <a class="el" href="a00332_source.html">quaternion_exponential.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
