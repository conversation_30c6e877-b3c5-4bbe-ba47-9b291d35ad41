<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: GLM_EXT_quaternion_relational</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_quaternion_relational<div class="ingroups"><a class="el" href="a00894.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad7827af0549504ff1cd6a359786acc7a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad7827af0549504ff1cd6a359786acc7a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00854.html#gad7827af0549504ff1cd6a359786acc7a">equal</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gad7827af0549504ff1cd6a359786acc7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x == y.  <a href="a00854.html#gad7827af0549504ff1cd6a359786acc7a">More...</a><br /></td></tr>
<tr class="separator:gad7827af0549504ff1cd6a359786acc7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa001eecb91106463169a8e5ef1577b39"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa001eecb91106463169a8e5ef1577b39"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00854.html#gaa001eecb91106463169a8e5ef1577b39">equal</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:gaa001eecb91106463169a8e5ef1577b39"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00854.html#gaa001eecb91106463169a8e5ef1577b39">More...</a><br /></td></tr>
<tr class="separator:gaa001eecb91106463169a8e5ef1577b39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab441cee0de5867a868f3a586ee68cfe1"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab441cee0de5867a868f3a586ee68cfe1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00854.html#gab441cee0de5867a868f3a586ee68cfe1">notEqual</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gab441cee0de5867a868f3a586ee68cfe1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x != y.  <a href="a00854.html#gab441cee0de5867a868f3a586ee68cfe1">More...</a><br /></td></tr>
<tr class="separator:gab441cee0de5867a868f3a586ee68cfe1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5117a44c1bf21af857cd23e44a96d313"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5117a44c1bf21af857cd23e44a96d313"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00854.html#ga5117a44c1bf21af857cd23e44a96d313">notEqual</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:ga5117a44c1bf21af857cd23e44a96d313"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00854.html#ga5117a44c1bf21af857cd23e44a96d313">More...</a><br /></td></tr>
<tr class="separator:ga5117a44c1bf21af857cd23e44a96d313"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes comparison functions for quaternion types that take a user defined epsilon values.</p>
<p>Include &lt;<a class="el" href="a00344.html" title="GLM_EXT_quaternion_relational">glm/ext/quaternion_relational.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd>core_vector_relational </dd>
<dd>
<a class="el" href="a00881.html">GLM_EXT_vector_relational</a> </dd>
<dd>
<a class="el" href="a00827.html">GLM_EXT_matrix_relational</a> </dd>
<dd>
<a class="el" href="a00851.html">GLM_EXT_quaternion_float</a> </dd>
<dd>
<a class="el" href="a00848.html">GLM_EXT_quaternion_double</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a id="gad7827af0549504ff1cd6a359786acc7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad7827af0549504ff1cd6a359786acc7a">&#9670;&nbsp;</a></span>equal() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::equal </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of result x == y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="gaa001eecb91106463169a8e5ef1577b39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa001eecb91106463169a8e5ef1577b39">&#9670;&nbsp;</a></span>equal() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::equal </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &lt; epsilon. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="gab441cee0de5867a868f3a586ee68cfe1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab441cee0de5867a868f3a586ee68cfe1">&#9670;&nbsp;</a></span>notEqual() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::notEqual </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of result x != y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga5117a44c1bf21af857cd23e44a96d313"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5117a44c1bf21af857cd23e44a96d313">&#9670;&nbsp;</a></span>notEqual() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::notEqual </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &gt;= epsilon. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
