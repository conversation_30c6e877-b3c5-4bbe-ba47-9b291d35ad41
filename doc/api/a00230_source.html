<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.18"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>1.0.0 API documentation: matrix_int3x3_sized.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">1.0.0 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.18 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_885cc87fac2d91e269af0a5a959fa5f6.html">E:</a></li><li class="navelem"><a class="el" href="dir_63ed049134a778d525e06b63afc2c979.html">Github</a></li><li class="navelem"><a class="el" href="dir_c98a9ac98258ab9f831b188d66361a70.html">g-truc</a></li><li class="navelem"><a class="el" href="dir_50f12b6ceb23d7f6adfb377a1ae8b006.html">glm</a></li><li class="navelem"><a class="el" href="dir_fca33f1b5115d46f42c670590789c0d2.html">glm</a></li><li class="navelem"><a class="el" href="dir_b171cecbb853a9ee4caace490047c53f.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_int3x3_sized.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00230.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_int_sized.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_int3x3_sized extension included&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160; </div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span>glm</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;{</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="a00816.html#gac6702a5f34779f892e8bcd4f9db8cc96">   31</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int8, defaultp&gt;                               <a class="code" href="a00816.html#gac6702a5f34779f892e8bcd4f9db8cc96">i8mat3x3</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="a00816.html#ga361b2094238f92774c8db5093b44566b">   36</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int16, defaultp&gt;                              <a class="code" href="a00816.html#ga361b2094238f92774c8db5093b44566b">i16mat3x3</a>;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="a00816.html#ga75af30ee79bc6f01be007e6762e369aa">   41</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int32, defaultp&gt;                              <a class="code" href="a00816.html#ga75af30ee79bc6f01be007e6762e369aa">i32mat3x3</a>;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00816.html#ga2d994eec234bb2f1f12d6c89519099e3">   46</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int64, defaultp&gt;                              <a class="code" href="a00816.html#ga2d994eec234bb2f1f12d6c89519099e3">i64mat3x3</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="a00816.html#ga09dd4ed01cbdbaabea98ee994b6ae578">   52</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int8, defaultp&gt;                               <a class="code" href="a00816.html#ga09dd4ed01cbdbaabea98ee994b6ae578">i8mat3</a>;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="a00816.html#ga7b4f37f881ec3362f134bd944560fe3f">   57</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int16, defaultp&gt;                              <a class="code" href="a00816.html#ga7b4f37f881ec3362f134bd944560fe3f">i16mat3</a>;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="a00816.html#gaeee8d25ed2061b68720bcb831327ffd4">   62</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int32, defaultp&gt;                              <a class="code" href="a00816.html#gaeee8d25ed2061b68720bcb831327ffd4">i32mat3</a>;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="a00816.html#ga94bbe4d004cd4507d370e529540e5c40">   67</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int64, defaultp&gt;                              <a class="code" href="a00816.html#ga94bbe4d004cd4507d370e529540e5c40">i64mat3</a>;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;}<span class="comment">//namespace glm</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aa00816_html_ga09dd4ed01cbdbaabea98ee994b6ae578"><div class="ttname"><a href="a00816.html#ga09dd4ed01cbdbaabea98ee994b6ae578">glm::i8mat3</a></div><div class="ttdeci">mat&lt; 3, 3, int8, defaultp &gt; i8mat3</div><div class="ttdoc">8 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00052">matrix_int3x3_sized.hpp:52</a></div></div>
<div class="ttc" id="aa00816_html_ga94bbe4d004cd4507d370e529540e5c40"><div class="ttname"><a href="a00816.html#ga94bbe4d004cd4507d370e529540e5c40">glm::i64mat3</a></div><div class="ttdeci">mat&lt; 3, 3, int64, defaultp &gt; i64mat3</div><div class="ttdoc">64 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00067">matrix_int3x3_sized.hpp:67</a></div></div>
<div class="ttc" id="aa00816_html_ga75af30ee79bc6f01be007e6762e369aa"><div class="ttname"><a href="a00816.html#ga75af30ee79bc6f01be007e6762e369aa">glm::i32mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int32, defaultp &gt; i32mat3x3</div><div class="ttdoc">32 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00041">matrix_int3x3_sized.hpp:41</a></div></div>
<div class="ttc" id="aa00816_html_ga361b2094238f92774c8db5093b44566b"><div class="ttname"><a href="a00816.html#ga361b2094238f92774c8db5093b44566b">glm::i16mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int16, defaultp &gt; i16mat3x3</div><div class="ttdoc">16 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00036">matrix_int3x3_sized.hpp:36</a></div></div>
<div class="ttc" id="aa00816_html_gaeee8d25ed2061b68720bcb831327ffd4"><div class="ttname"><a href="a00816.html#gaeee8d25ed2061b68720bcb831327ffd4">glm::i32mat3</a></div><div class="ttdeci">mat&lt; 3, 3, int32, defaultp &gt; i32mat3</div><div class="ttdoc">32 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00062">matrix_int3x3_sized.hpp:62</a></div></div>
<div class="ttc" id="aa00816_html_ga7b4f37f881ec3362f134bd944560fe3f"><div class="ttname"><a href="a00816.html#ga7b4f37f881ec3362f134bd944560fe3f">glm::i16mat3</a></div><div class="ttdeci">mat&lt; 3, 3, int16, defaultp &gt; i16mat3</div><div class="ttdoc">16 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00057">matrix_int3x3_sized.hpp:57</a></div></div>
<div class="ttc" id="aa00816_html_gac6702a5f34779f892e8bcd4f9db8cc96"><div class="ttname"><a href="a00816.html#gac6702a5f34779f892e8bcd4f9db8cc96">glm::i8mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int8, defaultp &gt; i8mat3x3</div><div class="ttdoc">8 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00031">matrix_int3x3_sized.hpp:31</a></div></div>
<div class="ttc" id="aa00816_html_ga2d994eec234bb2f1f12d6c89519099e3"><div class="ttname"><a href="a00816.html#ga2d994eec234bb2f1f12d6c89519099e3">glm::i64mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int64, defaultp &gt; i64mat3x3</div><div class="ttdoc">64 bit signed integer 3x3 matrix.</div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00046">matrix_int3x3_sized.hpp:46</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.18
</small></address>
</body>
</html>
