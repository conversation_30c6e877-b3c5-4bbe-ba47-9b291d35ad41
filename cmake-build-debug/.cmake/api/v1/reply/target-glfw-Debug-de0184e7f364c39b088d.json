{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Donut/thirdparty/glfw/src/glfw3.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "add_definitions", "target_compile_definitions", "target_include_directories", "set_target_properties", "target_sources"], "files": ["External/Donut/thirdparty/glfw/src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"command": 1, "file": 0, "line": 251, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 49, "parent": 3}, {"command": 3, "file": 0, "line": 260, "parent": 0}, {"command": 3, "file": 0, "line": 283, "parent": 0}, {"command": 3, "file": 0, "line": 40, "parent": 0}, {"command": 4, "file": 0, "line": 132, "parent": 0}, {"command": 4, "file": 0, "line": 135, "parent": 0}, {"command": 5, "file": 0, "line": 122, "parent": 0}, {"command": 6, "file": 0, "line": 16, "parent": 0}, {"command": 6, "file": 0, "line": 41, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MTd"}, {"backtrace": 2, "fragment": "/W3"}, {"fragment": "-WX"}], "defines": [{"backtrace": 4, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}, {"backtrace": 5, "define": "UNICODE"}, {"backtrace": 6, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 7, "define": "_GLFW_WIN32"}, {"backtrace": 5, "define": "_UNICODE"}], "includes": [{"backtrace": 8, "path": "F:/RTXPT/External/Donut/thirdparty/glfw/include"}, {"backtrace": 9, "path": "F:/RTXPT/External/Donut/thirdparty/glfw/src"}, {"backtrace": 9, "path": "F:/RTXPT/cmake-build-debug/External/Donut/thirdparty/glfw/src"}], "language": "C", "languageStandard": {"backtraces": [10], "standard": "99"}, "sourceIndexes": [5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 22, 23, 24, 27, 28, 29, 30, 31]}], "folder": {"name": "Third-Party Libraries"}, "id": "glfw::@cde7473d92b8787751e5", "name": "glfw", "nameOnDisk": "glfw3.lib", "paths": {"build": "External/Donut/thirdparty/glfw/src", "source": "External/Donut/thirdparty/glfw/src"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 14, 15, 20, 21, 25, 26]}, {"name": "Source Files", "sourceIndexes": [5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 22, 23, 24, 27, 28, 29, 30, 31]}], "sources": [{"backtrace": 1, "path": "External/Donut/thirdparty/glfw/include/GLFW/glfw3.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/glfw/include/GLFW/glfw3native.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/glfw/src/internal.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/glfw/src/platform.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/glfw/src/mappings.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/context.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/init.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/input.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/monitor.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/platform.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/vulkan.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/window.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/egl_context.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/osmesa_context.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Donut/thirdparty/glfw/src/null_platform.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Donut/thirdparty/glfw/src/null_joystick.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/null_init.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/null_monitor.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/null_window.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/null_joystick.c", "sourceGroupIndex": 1}, {"backtrace": 11, "path": "External/Donut/thirdparty/glfw/src/win32_time.h", "sourceGroupIndex": 0}, {"backtrace": 11, "path": "External/Donut/thirdparty/glfw/src/win32_thread.h", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_module.c", "sourceGroupIndex": 1}, {"backtrace": 11, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_time.c", "sourceGroupIndex": 1}, {"backtrace": 11, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_thread.c", "sourceGroupIndex": 1}, {"backtrace": 12, "path": "External/Donut/thirdparty/glfw/src/win32_platform.h", "sourceGroupIndex": 0}, {"backtrace": 12, "path": "External/Donut/thirdparty/glfw/src/win32_joystick.h", "sourceGroupIndex": 0}, {"backtrace": 12, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_init.c", "sourceGroupIndex": 1}, {"backtrace": 12, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_joystick.c", "sourceGroupIndex": 1}, {"backtrace": 12, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_monitor.c", "sourceGroupIndex": 1}, {"backtrace": 12, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/win32_window.c", "sourceGroupIndex": 1}, {"backtrace": 12, "compileGroupIndex": 0, "path": "External/Donut/thirdparty/glfw/src/wgl_context.c", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}