shaders/omm_clear_buffer.cs.hlsl -T cs -E main 
shaders/omm_compress.cs.hlsl -T cs -E main 
shaders/omm_desc_patch.cs.hlsl -T cs -E main 
shaders/omm_index_write.cs.hlsl -T cs -E main 
shaders/omm_init_buffers_cs.cs.hlsl -T cs -E main 
shaders/omm_init_buffers_gfx.cs.hlsl -T cs -E main 
shaders/omm_post_build_info.cs.hlsl -T cs -E main 
shaders/omm_rasterize.gs.hlsl -T gs -E main 
shaders/omm_rasterize.vs.hlsl -T vs -E main 
shaders/omm_rasterize_cs_a.cs.hlsl -T cs -E main 
shaders/omm_rasterize_cs_b.cs.hlsl -T cs -E main 
shaders/omm_rasterize_cs_g.cs.hlsl -T cs -E main 
shaders/omm_rasterize_cs_r.cs.hlsl -T cs -E main 
shaders/omm_rasterize_debug.ps.hlsl -T ps -E main 
shaders/omm_rasterize_debug.vs.hlsl -T vs -E main 
shaders/omm_rasterize_ps_a.ps.hlsl -T ps -E main 
shaders/omm_rasterize_ps_b.ps.hlsl -T ps -E main 
shaders/omm_rasterize_ps_g.ps.hlsl -T ps -E main 
shaders/omm_rasterize_ps_r.ps.hlsl -T ps -E main 
shaders/omm_render_target_clear.ps.hlsl -T ps -E main 
shaders/omm_work_setup_bake_only_cs.cs.hlsl -T cs -E main 
shaders/omm_work_setup_bake_only_gfx.cs.hlsl -T cs -E main 
shaders/omm_work_setup_cs.cs.hlsl -T cs -E main 
shaders/omm_work_setup_gfx.cs.hlsl -T cs -E main 
