{"version": 6, "cmakeMinimumRequired": {"major": 3, "minor": 5, "patch": 0}, "configurePresets": [{"name": "gcc", "displayName": "GCC", "description": "Default build options for GCC", "generator": "Unix Makefiles", "binaryDir": "${sourceDir}/build"}], "buildPresets": [{"name": "gcc", "configurePreset": "gcc"}], "testPresets": [{"name": "gcc", "configurePreset": "gcc", "output": {"outputOnFailure": true}, "execution": {"noTestsAction": "error", "stopOnFailure": true}}]}