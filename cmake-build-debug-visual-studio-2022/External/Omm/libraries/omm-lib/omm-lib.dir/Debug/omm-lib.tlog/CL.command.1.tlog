^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\BAKE.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\BAKE.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\BAKE_CPU_IMPL.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\BAKE_CPU_IMPL.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\BAKE_GPU_IMPL.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\BAKE_GPU_IMPL.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\DEBUG_IMPL.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\DEBUG_IMPL.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\SERIALIZE_IMPL.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\SERIALIZE_IMPL.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\SHADER_BINDINGS.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\SHADER_BINDINGS.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\STB_LIB.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\STB_LIB.CPP
^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\TEXTURE_IMPL.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SHADERS" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D WIN32_LEAN_AND_MEAN /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D _UNICODE /D UNICODE /D "OMM_API=extern \"C\" __declspec(dllexport)" /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D OMM_VK_S_SHIFT=100 /D OMM_VK_T_SHIFT=200 /D OMM_VK_B_SHIFT=300 /D OMM_VK_U_SHIFT=400 /D "CMAKE_INTDIR=\"Debug\"" /D omm_lib_EXPORTS /EHsc /RTC1 /MTd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-LIB.DIR\DEBUG\\" /Fd"OMM-LIB.DIR\DEBUG\VC143.PDB" /external:W1 /Gd /TP F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\SRC\TEXTURE_IMPL.CPP
