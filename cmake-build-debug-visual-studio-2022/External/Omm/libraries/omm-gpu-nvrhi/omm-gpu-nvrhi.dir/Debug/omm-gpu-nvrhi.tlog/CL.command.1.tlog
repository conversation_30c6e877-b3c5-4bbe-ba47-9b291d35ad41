^F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI\OMM-GPU-NVRHI.CPP
/c /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /I"F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /IF:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /IF:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /Zi /nologo /W1 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"OMM-GPU-NVRHI.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI\DEBUG\OMM-GPU-NVRHI.PDB" /external:W0 /Gd /TP  /external:I "F:/RTXPT/External/nvapi" F:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI\OMM-GPU-NVRHI.CPP
