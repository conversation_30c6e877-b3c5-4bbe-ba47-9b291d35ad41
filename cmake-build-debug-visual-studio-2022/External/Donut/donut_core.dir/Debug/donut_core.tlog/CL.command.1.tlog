^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\CHUNK\CHUNKFILE.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSO<PERSON>PP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\CHUNK\CHUNKFILE.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\CHUNK\CHUNKREADER.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\CHUNK\CHUNKREADER.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\CHUNK\CHUNKWRITER.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\CHUNK\CHUNKWRITER.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\JSON.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\JSON.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\LOG.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\LOG.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\COLOR.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\COLOR.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\FRUSTUM.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\FRUSTUM.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\MATRIX.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\MATRIX.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\VECTOR.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\MATH\VECTOR.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\STRING_UTILS.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\STRING_UTILS.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\COMPRESSION.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\COMPRESSION.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\TARFILE.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\TARFILE.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\VFS.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\VFS.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\WINRESFS.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\WINRESFS.CPP
^F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\ZIPFILE.CPP
/c /IF:\RTXPT\EXTERNAL\DONUT\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /IF:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /Zi /nologo /W3 /WX /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D _ITERATOR_DEBUG_LEVEL=1 /D _DEBUG /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D MINIZ_STATIC_DEFINE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"DONUT_CORE.DIR\DEBUG\\" /Fd"F:\RTXPT\CMAKE-BUILD-DEBUG-VISUAL-STUDIO-2022\EXTERNAL\DONUT\DEBUG\DONUT_CORE.PDB" /external:W3 /Gd /TP F:\RTXPT\EXTERNAL\DONUT\SRC\CORE\VFS\ZIPFILE.CPP
