<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A24F3692-E332-3A6C-A79C-360F28E29058}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>donut_app</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">donut_app.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">donut_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">donut_app.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">donut_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\RTXPT\External\Donut\include;F:\RTXPT\External\Donut\src\app\streamline;F:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\..\..\include;F:\RTXPT\External\Donut\thirdparty\miniz;F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz;F:\RTXPT\External\Donut\nvrhi\include;F:\RTXPT\External\Donut\thirdparty\stb;F:\RTXPT\External\Donut\thirdparty\cgltf;F:\RTXPT\External\Donut\ShaderMake\include;F:\RTXPT\External\Donut\thirdparty\taskflow;F:\RTXPT\External\Donut\thirdparty\tinyexr;F:\RTXPT\External\Donut\thirdparty\glfw\include;F:\RTXPT\External\Donut\thirdparty\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/RTXPT/External/Streamline/include" /external:I "F:/RTXPT/External/nvapi"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;_ITERATOR_DEBUG_LEVEL=1;_DEBUG;STREAMLINE_FEATURE_DLSS_SR;STREAMLINE_FEATURE_DLSS_FG;STREAMLINE_FEATURE_DLSS_RR;STREAMLINE_FEATURE_IMGUI;STREAMLINE_FEATURE_REFLEX;DONUT_WITH_AFTERMATH=0;DONUT_WITH_STREAMLINE=1;DONUT_FORCE_DISCRETE_GPU=0;RTXPT_LOCAL_CONFIG_ID_STRING=std::string("NONAME");NOMINMAX;_CRT_SECURE_NO_WARNINGS;DONUT_WITH_MINIZ;MINIZ_STATIC_DEFINE;DONUT_WITH_TASKFLOW;DONUT_WITH_TINYEXR;DONUT_WITH_DX11=0;DONUT_WITH_DX12=1;DONUT_WITH_VULKAN=0;DONUT_WITH_STATIC_SHADERS=0;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;_ITERATOR_DEBUG_LEVEL=1;STREAMLINE_FEATURE_DLSS_SR;STREAMLINE_FEATURE_DLSS_FG;STREAMLINE_FEATURE_DLSS_RR;STREAMLINE_FEATURE_IMGUI;STREAMLINE_FEATURE_REFLEX;DONUT_WITH_AFTERMATH=0;DONUT_WITH_STREAMLINE=1;DONUT_FORCE_DISCRETE_GPU=0;RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\");NOMINMAX;_CRT_SECURE_NO_WARNINGS;DONUT_WITH_MINIZ;MINIZ_STATIC_DEFINE;DONUT_WITH_TASKFLOW;DONUT_WITH_TINYEXR;DONUT_WITH_DX11=0;DONUT_WITH_DX12=1;DONUT_WITH_VULKAN=0;DONUT_WITH_STATIC_SHADERS=0;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\RTXPT\External\Donut\include;F:\RTXPT\External\Donut\src\app\streamline;F:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\..\..\include;F:\RTXPT\External\Donut\thirdparty\miniz;F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz;F:\RTXPT\External\Donut\nvrhi\include;F:\RTXPT\External\Donut\thirdparty\stb;F:\RTXPT\External\Donut\thirdparty\cgltf;F:\RTXPT\External\Donut\ShaderMake\include;F:\RTXPT\External\Donut\thirdparty\taskflow;F:\RTXPT\External\Donut\thirdparty\tinyexr;F:\RTXPT\External\Donut\thirdparty\glfw\include;F:\RTXPT\External\Donut\thirdparty\imgui;F:\RTXPT\External\Streamline\include;F:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\RTXPT\External\Donut\include;F:\RTXPT\External\Donut\src\app\streamline;F:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\..\..\include;F:\RTXPT\External\Donut\thirdparty\miniz;F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz;F:\RTXPT\External\Donut\nvrhi\include;F:\RTXPT\External\Donut\thirdparty\stb;F:\RTXPT\External\Donut\thirdparty\cgltf;F:\RTXPT\External\Donut\ShaderMake\include;F:\RTXPT\External\Donut\thirdparty\taskflow;F:\RTXPT\External\Donut\thirdparty\tinyexr;F:\RTXPT\External\Donut\thirdparty\glfw\include;F:\RTXPT\External\Donut\thirdparty\imgui;F:\RTXPT\External\Streamline\include;F:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\RTXPT\External\Donut\include;F:\RTXPT\External\Donut\src\app\streamline;F:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\..\..\include;F:\RTXPT\External\Donut\thirdparty\miniz;F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz;F:\RTXPT\External\Donut\nvrhi\include;F:\RTXPT\External\Donut\thirdparty\stb;F:\RTXPT\External\Donut\thirdparty\cgltf;F:\RTXPT\External\Donut\ShaderMake\include;F:\RTXPT\External\Donut\thirdparty\taskflow;F:\RTXPT\External\Donut\thirdparty\tinyexr;F:\RTXPT\External\Donut\thirdparty\glfw\include;F:\RTXPT\External\Donut\thirdparty\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/RTXPT/External/Streamline/include" /external:I "F:/RTXPT/External/nvapi"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NDEBUG;STREAMLINE_FEATURE_DLSS_SR;STREAMLINE_FEATURE_DLSS_FG;STREAMLINE_FEATURE_DLSS_RR;STREAMLINE_FEATURE_IMGUI;STREAMLINE_FEATURE_REFLEX;DONUT_WITH_AFTERMATH=0;DONUT_WITH_STREAMLINE=1;DONUT_FORCE_DISCRETE_GPU=0;RTXPT_LOCAL_CONFIG_ID_STRING=std::string("NONAME");NOMINMAX;_CRT_SECURE_NO_WARNINGS;DONUT_WITH_MINIZ;MINIZ_STATIC_DEFINE;DONUT_WITH_TASKFLOW;DONUT_WITH_TINYEXR;DONUT_WITH_DX11=0;DONUT_WITH_DX12=1;DONUT_WITH_VULKAN=0;DONUT_WITH_STATIC_SHADERS=0;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NDEBUG;STREAMLINE_FEATURE_DLSS_SR;STREAMLINE_FEATURE_DLSS_FG;STREAMLINE_FEATURE_DLSS_RR;STREAMLINE_FEATURE_IMGUI;STREAMLINE_FEATURE_REFLEX;DONUT_WITH_AFTERMATH=0;DONUT_WITH_STREAMLINE=1;DONUT_FORCE_DISCRETE_GPU=0;RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\");NOMINMAX;_CRT_SECURE_NO_WARNINGS;DONUT_WITH_MINIZ;MINIZ_STATIC_DEFINE;DONUT_WITH_TASKFLOW;DONUT_WITH_TINYEXR;DONUT_WITH_DX11=0;DONUT_WITH_DX12=1;DONUT_WITH_VULKAN=0;DONUT_WITH_STATIC_SHADERS=0;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\RTXPT\External\Donut\include;F:\RTXPT\External\Donut\src\app\streamline;F:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\..\..\include;F:\RTXPT\External\Donut\thirdparty\miniz;F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz;F:\RTXPT\External\Donut\nvrhi\include;F:\RTXPT\External\Donut\thirdparty\stb;F:\RTXPT\External\Donut\thirdparty\cgltf;F:\RTXPT\External\Donut\ShaderMake\include;F:\RTXPT\External\Donut\thirdparty\taskflow;F:\RTXPT\External\Donut\thirdparty\tinyexr;F:\RTXPT\External\Donut\thirdparty\glfw\include;F:\RTXPT\External\Donut\thirdparty\imgui;F:\RTXPT\External\Streamline\include;F:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\RTXPT\External\Donut\include;F:\RTXPT\External\Donut\src\app\streamline;F:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\..\..\include;F:\RTXPT\External\Donut\thirdparty\miniz;F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz;F:\RTXPT\External\Donut\nvrhi\include;F:\RTXPT\External\Donut\thirdparty\stb;F:\RTXPT\External\Donut\thirdparty\cgltf;F:\RTXPT\External\Donut\ShaderMake\include;F:\RTXPT\External\Donut\thirdparty\taskflow;F:\RTXPT\External\Donut\thirdparty\tinyexr;F:\RTXPT\External\Donut\thirdparty\glfw\include;F:\RTXPT\External\Donut\thirdparty\imgui;F:\RTXPT\External\Streamline\include;F:\RTXPT\External\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\RTXPT\External\Donut\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/RTXPT/External/Donut/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\bin\cmake.exe" -SF:/RTXPT -BF:/RTXPT/cmake-build-debug-visual-studio-2022 --check-stamp-file F:/RTXPT/cmake-build-debug-visual-studio-2022/External/Donut/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDependentOption.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\FindPackageMessage.cmake;F:\RTXPT\External\Donut\cmake\FindStreamline.cmake;F:\RTXPT\External\Donut\donut-app.cmake;F:\RTXPT\External\Donut\donut-core.cmake;F:\RTXPT\External\Donut\donut-engine.cmake;F:\RTXPT\External\Donut\donut-render.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/RTXPT/External/Donut/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\bin\cmake.exe" -SF:/RTXPT -BF:/RTXPT/cmake-build-debug-visual-studio-2022 --check-stamp-file F:/RTXPT/cmake-build-debug-visual-studio-2022/External/Donut/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDependentOption.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\FindPackageMessage.cmake;F:\RTXPT\External\Donut\cmake\FindStreamline.cmake;F:\RTXPT\External\Donut\donut-app.cmake;F:\RTXPT\External\Donut\donut-core.cmake;F:\RTXPT\External\Donut\donut-engine.cmake;F:\RTXPT\External\Donut\donut-render.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\AftermathCrashDump.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\ApplicationBase.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\Camera.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\DeviceManager.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\DeviceManager_DX11.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\DeviceManager_DX12.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\DeviceManager_VK.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\MediaFileSystem.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\StreamlineInterface.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\Timer.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\UserInterfaceUtils.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\imgui_console.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\imgui_nvrhi.h" />
    <ClInclude Include="F:\RTXPT\External\Donut\include\donut\app\imgui_renderer.h" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\ApplicationBase.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\Camera.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\DeviceManager.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\MediaFileSystem.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\UserInterfaceUtils.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\imgui_console.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\imgui_nvrhi.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\imgui_renderer.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\streamline\StreamlineIntegration.cpp" />
    <ClCompile Include="F:\RTXPT\External\Donut\src\app\dx12\DeviceManager_DX12.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\ZERO_CHECK.vcxproj">
      <Project>{F6099CF2-FD75-3FCF-B16E-C6E550B7E87B}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\streamline\CopyStreamlineDLLs.vcxproj">
      <Project>{B7B83CC2-D9A5-3754-9B00-D2778B37CF18}</Project>
      <Name>CopyStreamlineDLLs</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\ShaderMake\ShaderMakeBlob.vcxproj">
      <Project>{A893ED74-9306-31F3-B648-79F6CE8A5007}</Project>
      <Name>ShaderMakeBlob</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\donut_core.vcxproj">
      <Project>{90519538-5672-3E79-A45A-0FAC26B68876}</Project>
      <Name>donut_core</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\donut_engine.vcxproj">
      <Project>{D908FF6A-6E99-3E94-B339-9794926EA9E0}</Project>
      <Name>donut_engine</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\shaders\donut_shaders.vcxproj">
      <Project>{34B0493C-A96D-366E-8492-8EDA9421CEA0}</Project>
      <Name>donut_shaders</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\glfw\src\glfw.vcxproj">
      <Project>{32EBE069-71DC-3327-928E-98355CFA5944}</Project>
      <Name>glfw</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\imgui.vcxproj">
      <Project>{A1380EBF-EB4E-3914-8873-08A8F91DF60A}</Project>
      <Name>imgui</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\jsoncpp\src\lib_json\jsoncpp_static.vcxproj">
      <Project>{93B75C3D-4748-33A7-ACC1-818B6A6E1BD1}</Project>
      <Name>jsoncpp_static</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz\miniz.vcxproj">
      <Project>{F05A9A61-F477-35BB-8B54-AE8B02EA205B}</Project>
      <Name>miniz</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\nvrhi\nvrhi.vcxproj">
      <Project>{59801D7D-0810-3D69-B13E-24364F318682}</Project>
      <Name>nvrhi</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\nvrhi\nvrhi_d3d12.vcxproj">
      <Project>{7C56118F-1CB2-36F9-A0DA-1F4299ACC408}</Project>
      <Name>nvrhi_d3d12</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>