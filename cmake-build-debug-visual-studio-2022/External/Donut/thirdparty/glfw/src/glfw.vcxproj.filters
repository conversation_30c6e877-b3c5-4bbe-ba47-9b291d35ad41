<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\input.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\monitor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\platform.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\vulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\window.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\egl_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\osmesa_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\null_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\null_monitor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\null_window.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_module.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_time.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_thread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_joystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_monitor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_window.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\wgl_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\platform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\mappings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\null_platform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\null_joystick.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_time.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_thread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_platform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\win32_joystick.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\RTXPT\External\Donut\thirdparty\glfw\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{EEF89C4B-C7C5-3C34-B61F-D3A5900FFDE8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{EC98865C-892F-302C-BC74-E3C01F633B31}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
