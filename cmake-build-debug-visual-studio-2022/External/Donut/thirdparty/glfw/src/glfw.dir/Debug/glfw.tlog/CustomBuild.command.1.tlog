^F:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\SRC\CMAKELISTS.TXT
setlocal
"D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\bin\cmake.exe" -SF:/RTXPT -BF:/RTXPT/cmake-build-debug-visual-studio-2022 --check-stamp-file F:/RTXPT/cmake-build-debug-visual-studio-2022/External/Donut/thirdparty/glfw/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
