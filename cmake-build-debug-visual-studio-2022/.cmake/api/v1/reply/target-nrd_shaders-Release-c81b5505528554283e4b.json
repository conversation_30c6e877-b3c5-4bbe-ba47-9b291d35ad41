{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "donut_compile_shaders", "add_custom_command"], "files": ["External/Donut/compileshaders.cmake", "External/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 81, "parent": 0}, {"command": 0, "file": 0, "line": 113, "parent": 1}, {"command": 2, "file": 0, "line": 179, "parent": 1}]}, "dependencies": [{"backtrace": 3, "id": "ShaderMake::@b014256a752891a2614a"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "NRD"}, "id": "nrd_shaders::@6dcf5859e8d89a406b62", "name": "nrd_shaders", "paths": {"build": "External", "source": "External"}, "sourceGroups": [{"name": "", "sourceIndexes": [0, 1]}, {"name": "CMake Rules", "sourceIndexes": [2]}], "sources": [{"backtrace": 2, "path": "External/Nrd/Shaders/Include", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "cmake-build-debug-visual-studio-2022/External/CMakeFiles/nrd_shaders", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug-visual-studio-2022/CMakeFiles/187ba9600bf0a818e6632306eee7e18a/nrd_shaders.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}