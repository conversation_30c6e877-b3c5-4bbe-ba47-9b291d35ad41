{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "donut_compile_shaders", "add_custom_command"], "files": ["External/Donut/compileshaders.cmake", "Rtxpt/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 36, "parent": 0}, {"command": 0, "file": 0, "line": 113, "parent": 1}, {"command": 2, "file": 0, "line": 179, "parent": 1}]}, "dependencies": [{"backtrace": 3, "id": "ShaderMake::@b014256a752891a2614a"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "!RTX Path Tracing"}, "id": "rtxpt_shaders::@b012b6e63bd5043e3e8b", "name": "rtxpt_shaders", "paths": {"build": "Rtxpt", "source": "Rtxpt"}, "sourceGroups": [{"name": "", "sourceIndexes": [0, 5, 6, 73, 74, 75, 96, 97, 98, 103]}, {"name": "Bindings", "sourceIndexes": [1, 2, 3, 4]}, {"name": "GPUSort", "sourceIndexes": [7]}, {"name": "Lighting\\Distant", "sourceIndexes": [8, 9, 10, 11, 12]}, {"name": "Lighting", "sourceIndexes": [13]}, {"name": "NRD", "sourceIndexes": [14]}, {"name": "OpacityMicroMap", "sourceIndexes": [15, 16]}, {"name": "PathTracer\\Lighting", "sourceIndexes": [17, 18, 19, 20, 21]}, {"name": "PathTracer\\Materials", "sourceIndexes": [22]}, {"name": "PathTracer", "sourceIndexes": [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 43, 53, 54]}, {"name": "PathTracer\\Rendering\\Materials", "sourceIndexes": [33, 34, 35, 36, 37, 38, 39, 40, 41]}, {"name": "PathTracer\\Rendering\\Volumes", "sourceIndexes": [42]}, {"name": "PathTracer\\Scene", "sourceIndexes": [44, 45, 50, 51, 52]}, {"name": "PathTracer\\Scene\\Material", "sourceIndexes": [46, 47, 48, 49]}, {"name": "PathTracer\\Utils", "sourceIndexes": [55, 56, 65, 66, 72]}, {"name": "PathTracer\\Utils\\Math", "sourceIndexes": [57, 58, 59, 60, 61, 62, 63, 64]}, {"name": "PathTracer\\Utils\\Sampling\\Pseudorandom", "sourceIndexes": [67, 68, 69, 70]}, {"name": "PathTracer\\Utils\\Sampling", "sourceIndexes": [71]}, {"name": "RTXDI", "sourceIndexes": [76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95]}, {"name": "ToneMapper", "sourceIndexes": [99, 100, 101, 102]}, {"name": "CMake Rules", "sourceIndexes": [104]}], "sources": [{"backtrace": 2, "path": "Rtxpt/AccumulationPass.hlsl", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/Bindings/LightingBindings.hlsli", "sourceGroupIndex": 1}, {"backtrace": 2, "path": "Rtxpt/Bindings/SamplerBindings.hlsli", "sourceGroupIndex": 1}, {"backtrace": 2, "path": "Rtxpt/Bindings/SceneBindings.hlsli", "sourceGroupIndex": 1}, {"backtrace": 2, "path": "Rtxpt/Bindings/ShaderResourceBindings.hlsli", "sourceGroupIndex": 1}, {"backtrace": 2, "path": "Rtxpt/DebugLines.hlsl", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/ExportVisibilityBuffer.hlsl", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/GPUSort/GPUSort.hlsl", "sourceGroupIndex": 2}, {"backtrace": 2, "path": "Rtxpt/Lighting/Distant/BC6UCompress.hlsl", "sourceGroupIndex": 3}, {"backtrace": 2, "path": "Rtxpt/Lighting/Distant/EnvMapBaker.hlsl", "sourceGroupIndex": 3}, {"backtrace": 2, "path": "Rtxpt/Lighting/Distant/EnvMapImportanceSamplingBaker.hlsl", "sourceGroupIndex": 3}, {"backtrace": 2, "path": "Rtxpt/Lighting/Distant/SampleProceduralSky.hlsli", "sourceGroupIndex": 3}, {"backtrace": 2, "path": "Rtxpt/Lighting/Distant/precomputed_sky.hlsli", "sourceGroupIndex": 3}, {"backtrace": 2, "path": "Rtxpt/Lighting/LightsBaker.hlsl", "sourceGroupIndex": 4}, {"backtrace": 2, "path": "Rtxpt/NRD/DenoiserNRD.hlsli", "sourceGroupIndex": 5}, {"backtrace": 2, "path": "Rtxpt/OpacityMicroMap/OmmDebug.hlsli", "sourceGroupIndex": 6}, {"backtrace": 2, "path": "Rtxpt/OpacityMicroMap/OmmGeometryDebugData.hlsli", "sourceGroupIndex": 6}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Lighting/EnvMap.hlsli", "sourceGroupIndex": 7}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Lighting/LightSampler.hlsli", "sourceGroupIndex": 7}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Lighting/LightShaping.hlsli", "sourceGroupIndex": 7}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Lighting/LightingAlgorithms.hlsli", "sourceGroupIndex": 7}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Lighting/PolymorphicLight.hlsli", "sourceGroupIndex": 7}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Materials/MaterialTypes.hlsli", "sourceGroupIndex": 8}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathPayload.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathState.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracer.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerBridge.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerDebug.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerHelpers.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerNEE.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerNestedDielectrics.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerStablePlanes.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/PathTracerTypes.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/BxDF.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/BxDFConfig.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/Fresnel.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/IBSDF.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/InteriorList.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/LobeType.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/Microfacet.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/StandardBSDF.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Materials/TexLODHelpers.hlsli", "sourceGroupIndex": 10}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Rendering/Volumes/HomogeneousVolumeSampler.hlsli", "sourceGroupIndex": 11}, {"backtrace": 2, "path": "Rtxpt/PathTracer/SampleGenerators.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/HitInfo.hlsli", "sourceGroupIndex": 12}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/HitInfoType.hlsli", "sourceGroupIndex": 12}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/Material/HomogeneousVolumeData.hlsli", "sourceGroupIndex": 13}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/Material/MaterialData.hlsli", "sourceGroupIndex": 13}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/Material/ShadingUtils.hlsli", "sourceGroupIndex": 13}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/Material/TextureSampler.hlsli", "sourceGroupIndex": 13}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/SceneDefines.hlsli", "sourceGroupIndex": 12}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/SceneTypes.hlsli", "sourceGroupIndex": 12}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Scene/ShadingData.hlsli", "sourceGroupIndex": 12}, {"backtrace": 2, "path": "Rtxpt/PathTracer/StablePlanes.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/StatelessSampleGenerators.hlsli", "sourceGroupIndex": 9}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/ColorHelpers.hlsli", "sourceGroupIndex": 14}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Geometry.hlsli", "sourceGroupIndex": 14}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/BitTricks.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/FormatConversion.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/HashUtils.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/MathConstants.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/MathHelpers.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/PackedFormats.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/Quaternion.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Math/Ray.hlsli", "sourceGroupIndex": 15}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/NoiseAndSequences.hlsli", "sourceGroupIndex": 14}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Packing.hlsli", "sourceGroupIndex": 14}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Sampling/Pseudorandom/LCG.hlsli", "sourceGroupIndex": 16}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Sampling/Pseudorandom/SplitMix64.hlsli", "sourceGroupIndex": 16}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Sampling/Pseudorandom/Xorshift32.hlsli", "sourceGroupIndex": 16}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Sampling/Pseudorandom/Xoshiro.hlsli", "sourceGroupIndex": 16}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Sampling/Sampling.hlsli", "sourceGroupIndex": 17}, {"backtrace": 2, "path": "Rtxpt/PathTracer/Utils/Utils.hlsli", "sourceGroupIndex": 14}, {"backtrace": 2, "path": "Rtxpt/PathTracerBridgeDonut.hlsli", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/PathTracerBridgeNull.hlsli", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/PostProcess.hlsl", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/RTXDI/DIFinalShading.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/FusedDIGIFinalShading.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/GBufferHelpers.hlsli", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/GIFinalShading.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/GISpatialResampling.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/GITemporalResampling.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/GenerateInitialSamples.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/HelperFunctions.hlsli", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/PolymorphicLightRTXDI.hlsli", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/PrepareLights.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/PreprocessEnvironmentMap.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/PresampleEnvironmentMap.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/PresampleLights.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/PresampleReGIR.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/RtxdiApplicationBridge.hlsli", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/SetSurfaceData.hlsli", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/SpatialResampling.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/SpatioTemporalResampling.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/SurfaceData.hlsli", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/RTXDI/TemporalResampling.hlsl", "sourceGroupIndex": 18}, {"backtrace": 2, "path": "Rtxpt/Sample.hlsl", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/SampleNull.hlsl", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/ShaderDebug.hlsli", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "Rtxpt/ToneMapper/ToneMapperParams.hlsli", "sourceGroupIndex": 19}, {"backtrace": 2, "path": "Rtxpt/ToneMapper/ToneMapping.hlsl", "sourceGroupIndex": 19}, {"backtrace": 2, "path": "Rtxpt/ToneMapper/ToneMapping.ps.hlsli", "sourceGroupIndex": 19}, {"backtrace": 2, "path": "Rtxpt/ToneMapper/luminance_ps.hlsl", "sourceGroupIndex": 19}, {"backtrace": 2, "isGenerated": true, "path": "cmake-build-debug-visual-studio-2022/Rtxpt/CMakeFiles/rtxpt_shaders", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug-visual-studio-2022/CMakeFiles/0cb7ef25c62f2eca29d2e2c2d57cdfe6/rtxpt_shaders.rule", "sourceGroupIndex": 20}], "type": "UTILITY"}