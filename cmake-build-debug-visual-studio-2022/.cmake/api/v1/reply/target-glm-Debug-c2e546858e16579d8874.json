{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "External/Omm/external/glm/glm/Debug/glm.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "add_definitions", "target_link_libraries"], "files": ["External/Omm/external/glm/glm/CMakeLists.txt", "External/Omm/external/glm/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 54, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 121, "parent": 2}, {"command": 1, "file": 1, "line": 142, "parent": 2}, {"file": 2}, {"command": 2, "file": 2, "line": 49, "parent": 5}, {"command": 3, "file": 0, "line": 64, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /MP /Zi /Ob0 /Od /RTC1 /D_ITERATOR_DEBUG_LEVEL=1 -std:c++20 -MTd"}, {"backtrace": 3, "fragment": "/Za"}, {"backtrace": 4, "fragment": "/fp:precise"}, {"fragment": "-WX"}], "defines": [{"backtrace": 6, "define": "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")"}], "includes": [{"backtrace": 7, "path": "F:/RTXPT/External/Omm/external/glm"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [28]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "External"}, "id": "glm::@3d8cf37488c348ec8d16", "name": "glm", "nameOnDisk": "glm.lib", "paths": {"build": "External/Omm/external/glm/glm", "source": "External/Omm/external/glm/glm"}, "sourceGroups": [{"name": "Text Files", "sourceIndexes": [0, 1, 2, 3]}, {"name": "", "sourceIndexes": [4]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}, {"name": "Core Files", "sourceIndexes": [28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89]}, {"name": "EXT Files", "sourceIndexes": [90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259]}, {"name": "GTC Files", "sourceIndexes": [260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296]}, {"name": "GTX Files", "sourceIndexes": [297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423]}, {"name": "SIMD Files", "sourceIndexes": [424, 425, 426, 427, 428, 429, 430, 431, 432, 433]}], "sources": [{"backtrace": 1, "path": "External/Omm/external/glm/CMakeLists.txt", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/external/glm/copying.txt", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/external/glm/manual.md", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/external/glm/readme.md", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "External/Omm/external/glm/util/glm.natvis", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/common.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/exponential.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/fwd.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/geometric.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/glm.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/integer.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat2x2.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat2x3.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat2x4.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat3x2.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat3x3.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat3x4.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat4x2.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat4x3.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/mat4x4.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/matrix.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/packing.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/trigonometric.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/vec2.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/vec3.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/vec4.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/vector_relational.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "External/Omm/external/glm/glm/detail/glm.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_common.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_common_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_exponential.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_exponential_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_geometric.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_geometric_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_integer.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_integer_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_matrix.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_matrix_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_packing.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_packing_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_trigonometric.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_trigonometric_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_vector_relational.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/func_vector_relational_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_half.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat2x2.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat2x3.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat2x4.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat3x2.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat3x3.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat3x4.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x2.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x3.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x4.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x4_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_quat.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_quat_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec1.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec2.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec3.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec4.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec_simd.inl", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/_features.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/_fixes.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/_noise.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/_swizzle.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/_swizzle_func.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/_vectorize.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/compute_common.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/compute_vector_decl.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/compute_vector_relational.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/qualifier.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/setup.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_float.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_half.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat2x2.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat2x3.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat2x4.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat3x2.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat3x3.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat3x4.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x2.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x3.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_mat4x4.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_quat.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec1.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec2.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec3.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/detail/type_vec4.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_clip_space.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_common.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_integer.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_projection.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_relational.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_transform.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_common.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_common_simd.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_exponential.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_geometric.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_relational.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_transform.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_trigonometric.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_common.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_constants.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_integer.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_packing.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_reciprocal.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_relational.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_ulp.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_common.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_integer.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_packing.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_reciprocal.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_relational.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_ulp.inl", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/_matrix_vectorize.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_clip_space.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_common.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double2x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double2x2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double2x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double2x3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double2x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double2x4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double3x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double3x2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double3x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double3x3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double3x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double3x4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double4x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double4x2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double4x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double4x3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double4x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_double4x4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float2x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float2x2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float2x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float2x3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float2x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float2x4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float3x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float3x2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float3x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float3x3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float3x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float3x4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float4x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float4x2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float4x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float4x3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float4x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_float4x4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int2x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int2x2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int2x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int2x3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int2x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int2x4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int3x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int3x2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int3x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int3x3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int3x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int3x4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int4x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int4x2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int4x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int4x3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int4x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_int4x4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_integer.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_projection.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_relational.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_transform.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint2x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint2x2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint2x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint2x3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint2x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint2x4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint3x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint3x2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint3x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint3x3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint3x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint3x4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint4x2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint4x2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint4x3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint4x3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint4x4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/matrix_uint4x4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_common.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_double.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_double_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_exponential.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_float.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_float_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_geometric.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_relational.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_transform.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/quaternion_trigonometric.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_common.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_constants.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_int_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_integer.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_packing.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_reciprocal.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_relational.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_uint_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/scalar_ulp.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool1.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool1_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_bool4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_common.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double1.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double1_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_double4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float1.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float1_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float2_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float3_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_float4_precision.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int1.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int1_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_int4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_integer.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_packing.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_reciprocal.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_relational.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint1.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint1_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint2.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint2_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint3.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint3_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint4.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_uint4_sized.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/ext/vector_ulp.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/bitfield.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/color_space.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/constants.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/epsilon.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/integer.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_access.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_inverse.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_transform.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/noise.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/packing.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/quaternion.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/quaternion_simd.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/random.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/round.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/type_precision.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/type_ptr.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/ulp.inl", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/bitfield.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/color_space.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/constants.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/epsilon.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/integer.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_access.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_integer.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_inverse.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/matrix_transform.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/noise.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/packing.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/quaternion.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/random.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/reciprocal.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/round.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/type_aligned.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/type_precision.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/type_ptr.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/ulp.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtc/vec1.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/associated_min_max.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/bit.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/closest_point.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/color_encoding.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/color_space.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/color_space_YCoCg.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/common.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/compatibility.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/component_wise.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/dual_quaternion.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/easing.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/euler_angles.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/extend.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/extended_min_max.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/exterior_product.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/fast_exponential.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/fast_square_root.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/fast_trigonometry.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/float_normalize.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/functions.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/gradient_paint.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/handed_coordinate_space.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/hash.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/integer.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/intersect.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/io.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/log_base.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_cross_product.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_decompose.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_factorisation.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_interpolation.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_major_storage.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_operation.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_query.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_transform_2d.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/mixed_product.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/norm.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/normal.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/normalize_dot.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/optimum_pow.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/orthonormalize.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/pca.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/perpendicular.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/polar_coordinates.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/projection.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/quaternion.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/raw_data.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/rotate_normalized_axis.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/rotate_vector.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/scalar_relational.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/spline.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/std_based_type.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/string_cast.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/structured_bindings.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/texture.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/transform.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/transform2.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/type_aligned.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/type_trait.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/vector_angle.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/vector_query.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/wrap.inl", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/associated_min_max.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/bit.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/closest_point.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/color_encoding.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/color_space.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/color_space_YCoCg.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/common.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/compatibility.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/component_wise.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/dual_quaternion.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/easing.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/euler_angles.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/extend.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/extended_min_max.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/exterior_product.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/fast_exponential.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/fast_square_root.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/fast_trigonometry.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/functions.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/gradient_paint.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/handed_coordinate_space.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/hash.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/integer.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/intersect.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/io.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/log_base.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_cross_product.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_decompose.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_factorisation.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_interpolation.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_major_storage.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_operation.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_query.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/matrix_transform_2d.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/mixed_product.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/norm.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/normal.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/normalize_dot.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/number_precision.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/optimum_pow.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/orthonormalize.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/pca.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/perpendicular.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/polar_coordinates.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/projection.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/quaternion.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/range.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/raw_data.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/rotate_normalized_axis.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/rotate_vector.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/scalar_multiplication.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/scalar_relational.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/spline.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/std_based_type.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/string_cast.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/structured_bindings.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/texture.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/transform.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/transform2.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/type_aligned.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/type_trait.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/vec_swizzle.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/vector_angle.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/vector_query.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/gtx/wrap.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/common.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/exponential.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/geometric.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/integer.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/matrix.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/neon.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/packing.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/platform.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/trigonometric.h", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "External/Omm/external/glm/glm/simd/vector_relational.h", "sourceGroupIndex": 7}], "type": "STATIC_LIBRARY"}