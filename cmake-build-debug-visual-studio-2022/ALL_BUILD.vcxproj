<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{33C32865-1304-3F87-AA82-1F96F56BFD52}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\RTXPT\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/RTXPT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\bin\cmake.exe" -SF:/RTXPT -BF:/RTXPT/cmake-build-debug-visual-studio-2022 --check-stamp-file F:/RTXPT/cmake-build-debug-visual-studio-2022/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCCompilerABI.c;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeRCInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeSystem.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeCCompiler.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeCXXCompiler.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeRCCompiler.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/RTXPT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\bin\cmake.exe" -SF:/RTXPT -BF:/RTXPT/cmake-build-debug-visual-studio-2022 --check-stamp-file F:/RTXPT/cmake-build-debug-visual-studio-2022/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCCompilerABI.c;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeRCInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeSystem.cmake.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\MSVC.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\Windows.cmake;D:\1Softwares\CLion 2024.3.1.1\bin\cmake\win\x64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeCCompiler.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeCXXCompiler.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeRCCompiler.cmake;F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\3.30.5\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\RTXPT\cmake-build-debug-visual-studio-2022\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\ZERO_CHECK.vcxproj">
      <Project>{F6099CF2-FD75-3FCF-B16E-C6E550B7E87B}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\streamline\CopyStreamlineDLLs.vcxproj">
      <Project>{B7B83CC2-D9A5-3754-9B00-D2778B37CF18}</Project>
      <Name>CopyStreamlineDLLs</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\_deps\mathlib-build\MathLib.vcxproj">
      <Project>{83EE925C-8559-35A8-95FF-ED92A51D9077}</Project>
      <Name>MathLib</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Nrd\NRD.vcxproj">
      <Project>{844759DB-3102-395E-BBCD-88B693C2A33A}</Project>
      <Name>NRD</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Nrd\NRDIntegration.vcxproj">
      <Project>{22AF4A31-3D27-3E44-AB83-A514DB512175}</Project>
      <Name>NRDIntegration</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\Rtxpt\Rtxpt.vcxproj">
      <Project>{F5ED9C8D-1405-3055-B9AF-A6F6889693F5}</Project>
      <Name>Rtxpt</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\ShaderMake\ShaderMake.vcxproj">
      <Project>{F397B52E-27EE-3DF0-A7AC-343FB482679C}</Project>
      <Name>ShaderMake</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\ShaderMake\ShaderMakeBlob.vcxproj">
      <Project>{A893ED74-9306-31F3-B648-79F6CE8A5007}</Project>
      <Name>ShaderMakeBlob</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\glfw\src\glfw.vcxproj">
      <Project>{32EBE069-71DC-3327-928E-98355CFA5944}</Project>
      <Name>glfw</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Omm\external\glm\glm\glm.vcxproj">
      <Project>{67AF9490-F287-39FB-A98C-C0EFC78BCB87}</Project>
      <Name>glm</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\imgui.vcxproj">
      <Project>{A1380EBF-EB4E-3914-8873-08A8F91DF60A}</Project>
      <Name>imgui</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\jsoncpp\src\lib_json\jsoncpp_static.vcxproj">
      <Project>{93B75C3D-4748-33A7-ACC1-818B6A6E1BD1}</Project>
      <Name>jsoncpp_static</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Omm\external\lz4\build\cmake\lz4_static.vcxproj">
      <Project>{2840F1A4-C1F9-316E-BBB5-82064E9E4659}</Project>
      <Name>lz4_static</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\thirdparty\miniz\miniz.vcxproj">
      <Project>{F05A9A61-F477-35BB-8B54-AE8B02EA205B}</Project>
      <Name>miniz</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\nvrhi\nvrhi.vcxproj">
      <Project>{59801D7D-0810-3D69-B13E-24364F318682}</Project>
      <Name>nvrhi</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Donut\nvrhi\nvrhi_d3d12.vcxproj">
      <Project>{7C56118F-1CB2-36F9-A0DA-1F4299ACC408}</Project>
      <Name>nvrhi_d3d12</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Omm\libraries\omm-gpu-nvrhi\omm-gpu-nvrhi.vcxproj">
      <Project>{9B453B24-3CE6-3724-96E8-496B0BC92492}</Project>
      <Name>omm-gpu-nvrhi</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Omm\libraries\omm-lib\omm-lib.vcxproj">
      <Project>{42546C1E-858C-310A-A36F-32A0729EDF62}</Project>
      <Name>omm-lib</Name>
    </ProjectReference>
    <ProjectReference Include="F:\RTXPT\cmake-build-debug-visual-studio-2022\External\Omm\external\xxHash\cmake_unofficial\xxhash.vcxproj">
      <Project>{3610E1A4-9964-32CD-B25B-273B4D6C7DE4}</Project>
      <Name>xxhash</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>